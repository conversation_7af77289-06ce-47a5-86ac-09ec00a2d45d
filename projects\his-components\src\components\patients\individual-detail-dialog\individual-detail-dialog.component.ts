import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component, Inject, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { EcmedWebapiModelsPatientmgmtSearchPatientType, PatientService } from 'ecmed-api/visitmgmt';
import { CommonService } from '../common.service';
import { PatientIndividualFormComponent} from '../patient-individual-form/patient-individual-form.component';
import { MaterialPackage} from '@his-components/utils';

@Component({
  selector: 'his-indv-detail-dlg',
  standalone: true,
  imports: [MaterialPackage, MatTableModule, CommonModule],
  templateUrl: './individual-detail-dialog.component.html',
  styleUrl: './individual-detail-dialog.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
})

export class IndividualDetailDialogComponent {

  @Input() showAddButton: boolean = true;

  constructor(public route: ActivatedRoute, public router: Router,
    public _dialog: MatDialog, public patientsApi: PatientService,
    public commonApi: CommonService, public dialog: MatDialogRef<IndividualDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any) {
    if (dialogData && dialogData.showAddButton !== undefined) {
      this.showAddButton = dialogData.showAddButton;
      console.log(this.showAddButton, 'dialogdatanew')
    }
  }

  ngOnInit() {
    let codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
    this.commonApi.fetchVisitData(codeTypes).subscribe(
      (response: any) => {
        this.idTypes = response;
      }
    );
  }

  displayedColumns: any = ["Hr", "name", "telephone", "actions"]
  source: any = []
  idTypes: any = []
  idNo: any = "";
  idType: any = "";
  name: any = "";
  cusType: any = ""

  public handleSearch(event:Event) {
    if (!this.name && !this.idNo) {
      return
    }
    let type: any = !this.name ? "ByIdNo" : (!this.idNo ? "ByName" : "All")
    if (this.dialogData?.title === "patient") {
      this.handleGetSearchPatientsApi(type)
    } else {
      this.handlePatientSearchIndividualGetApi(type)
    }
  }

  public handlePatientSearchIndividualGetApi(type: string) {
  this.cusType = "individual";
  this.patientsApi.patientSearchIndividualGet({
    searchType: type as EcmedWebapiModelsPatientmgmtSearchPatientType,
    name: this.name,
    iDNo: this.idNo,
    iDType: this.idType
  }).subscribe({
    next: (res) => {
      this.source = res;
    },
    error: (err) => {
      console.error('Error in patientSearchIndividualGet:', err);
      this.source = [];
    }
  });
}

  public handleGetSearchPatientsApi(type:any) {
    this.patientsApi.patientSearchPatientsGet({
      searchType: type.toString(), 
      iDNo: this.idNo,
      iDType: this.idType?.toString()
    }).subscribe({
      next: (res) => {
        if (res.length) {
          this.cusType = "patient"
          this.source = res
        } else {
          this.handlePatientSearchIndividualGetApi(type)
        }
      },
      error: (err) => {
        this.handlePatientSearchIndividualGetApi(type)
        console.log(err)
      }
    })
  }

  public handleReset() {
    this.source = []
    this.idType = ""
    this.idNo = ""
    this.name = ""
  }

  public handleOnChange(type:string, event:Event) {
    switch (type)
    {
      case 'no':
       this.idNo =  (<HTMLInputElement>event.target).value;
       break;
      case 'name': 
         this.name = (<HTMLInputElement>event.target).value;
         break;
      case "type":
         this.idType = event
    }
  }

  public handleClose(ele:any) {
    this.dialog.close({
      data: {
        element: ele,
        type: this.cusType
      }
    })
  }

  public handleAdd() {
    this.dialog.close()
    this.router.navigate(["/patients/addIndividual/" + this.dialog?.id])
  }

  openDialog() {
    const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.patientsApi.patientSearchIndividualGet({searchType:"ByIdNo" as any, 
          iDNo: result.IDNo, iDType:result.IDType?.toString()}).subscribe({
          next: (res) => {
            this.source = res
          },
          error: (err) => {
            console.error('Error in openDialog patientSearchIndividualGet:', err);
            this.source = [];
          }
        })
      }
    });
  }

  edit(element:any): void {
    const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
      data: element
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.patientsApi.patientSearchIndividualGet({searchType:"ByIdNo", iDNo: result.IDNo, iDType:result.IDType?.toString()}).subscribe({
          next: (res) => {
            this.source = res
          },
          error: (err) => {
            console.error('Error in edit patientSearchIndividualGet:', err);
            this.source = [];
          }
        })
      }
    });
  }
}