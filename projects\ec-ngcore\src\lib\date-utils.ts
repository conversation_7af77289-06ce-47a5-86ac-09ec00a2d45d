export class dateUtils
{
   static truncTime (dt:Date):Date
   {
      let result = new Date(dt);
      result.setHours(0,0,0,0);
      return result;
   }
   static startOfMonth(dt?:Date):Date
   {
      if (!dt) dt = new Date();
      else dt = new Date(dt);
      return new Date(dt.getFullYear(), dt.getMonth(), 1);
   }
   static endOfMonth(dt?:Date):Date
   {
      if (!dt) dt = new Date();
      else dt = new Date(dt);
      return new Date(dt.getFullYear(), dt.getMonth()+1, 0);
   }
   static startOfWeek(dt?:Date):Date
   {
      if (!dt) dt = new Date();
      else dt = new Date(dt);
      const day = dt.getDay(),
      diff = dt.getDate() - day + (day == 0 ? -6 : 1); // adjust when day is sunday
      return new Date(dt.setDate(diff));
   }

   static endOfWeek(dt?:Date):Date
   {
      if (!dt) dt = new Date();
      else dt = new Date(dt);
      const day = dt.getDay(),
      diff = dt.getDate() + (7-day); 
      return new Date(dt.setDate(diff));
   }

   static addDays(dt:Date, num:number):Date
   {
       dt = new Date(dt); //clone
       return new Date(dt.setDate(dt.getDate()+(num)));
   }
   static addMonths(dt:Date, num:number):Date
   {
       let result = new Date(dt);
       return new Date(result.setMonth(result.getMonth()+num));
   }
   static toISODate(dt?:Date|null) : string|undefined
   {
      if (!(dt)) return undefined;
      const year = dt.getFullYear();
      const month = (dt.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-indexed
      const day = dt.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
   }
}
