import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MaterialPackage } from '@app-his/utils';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { PatientService, VisitService } from 'ecmed-api/visitmgmt';
import { BillingService, OrderService } from 'ecmed-api/billing';
import { VisitServicesView, AddReceiptComponent,
         ArPayDialogComponent,ThirdPartySearchComponent
} from '@app-his/components';
import { ApplicationService } from 'ec-ngcore';
import {  FillHeightDirective} from 'ec-ngcore/ui';
import { DatePipe, DecimalPipe } from '@angular/common';

@Component({
  standalone: true,
  imports: [ FillHeightDirective, VisitServicesView, MaterialPackage],
  templateUrl: './visitdata.component.html',
  styleUrl: './visitdata.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
})
export class VisitdataComponent {
  patientId?: string;
  patient: any;
  appList: any;
  id: any;
  appoinmentId?: string;
  visitId?: string;
  isPrintClicked: boolean = false;
  visitDetails: any;
  totalservices: any;
  totalbill: any;
  payerDetail: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _Api: PatientService,
    public _dialog: MatDialog,
    public _API: OrderService,
    public billingapi: BillingService,
    public _HISAPI: AppointmentsAPIService,
    public visit: VisitService, private _appService:ApplicationService) { }

  ngOnInit() {
    this.route.params.subscribe(params => {
      const id = params['id'];
      this.id = id;
      this.getvisitviewid();
      this.serviceView();
      this.serviceViewbilling();
      this.payerDetails();
    });
  }
  serviceView() {
    this._API.orderGetVisitServicesCountGet(this.id).subscribe(
      (result) => {
        this.totalservices = result
      }
    )
  }
  serviceViewbilling() {
    this.billingapi.billingGetReceiptsCountGet(this.id).subscribe(
      (result) => {
        this.totalbill = result
      }
    )
  }

  payerDetails() {
    const patid = this.patient?.[0]?.PAT_IDENTIFIER
    this.billingapi.billingGetPayerDetailssGet(patid).subscribe(
      (result) => {
        this.payerDetail = result;
      }
    )

  }

  onPrint() {
    this.isPrintClicked = true;
    window.print();
  }

  public getvisitviewid() {
    this.visit.visitGetVisitAppointmentDetailsGet(this.id).subscribe(
      (result:any[]) => {
        this.patient = result;
        if (result.length) {
          this.patientId = result[0]["PAT_IDENTIFIER"]
        }
        this.payerDetails();
      }
    );
  }

  handleArPayment() {
    const dialogRef = ArPayDialogComponent.openDialog(this._dialog, { hrn: this.patient.length ? this.patient[0]?.HRN : '', name: this.patient.length ? this.patient[0]?.NAME : '', patId: this.patient[0]?.PAT_IDENTIFIER, obj: this.payerDetail });
    dialogRef.afterClosed().subscribe((result) => {
      this.payerDetails();
    })
  }

  handleThirdPartyDialog() {
    let dialogRef = this._dialog.open(ThirdPartySearchComponent, {
      width: '80%',
      height: '70%',
      data: { patientId: this.patientId, visitId: this.id }
    });
  }

  goback() {
    this.router.navigate(['his-psd/service'])
  }

  edit(id: any) {
    this.router.navigate(['his-psd/service/patients/edit/' + id]);
  }
  editVisit2(data: any) {
    this.router.navigate(['appointmentqueue/add', { IDENTIFIER: "1169", INDIVIDUALID: "2478" }])
  }
  editvisit1(id: any) {
    this.router.navigate(['his-psd/service/edit/' + id]);
  }

  editvisit() {
    if (this.patient && this.patient.length > 0) {
      const visitIdentifier = this.patient[0].VISIT_IDENTIFIER;

      this.router.navigate(['his-psd/service/edit/' + visitIdentifier]);
    }
  }

  showPopup() {

    this._appService.alertDialog({message:'Under Construction'});
  }

  public handleReceiptPopupOpen() {
    let dialogRef = this._dialog.open(AddReceiptComponent, {
      disableClose: true,
      autoFocus: true,
      width: '900px',
      data: {
        visitID: this.id,
        patientID: this.patientId
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      this.serviceViewbilling()
    });

  }



}
