import { Component, Input } from '@angular/core';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { Router, ActivatedRoute } from '@angular/router';
import { ApptSearchFormComponent, ApptSearchListComponent } from '@app-his/components';
import { CommonService } from 'his-components';
import { MaterialPackage } from '@app-his/utils';
import { dateUtils, ApplicationService } from 'ec-ngcore';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { DateRange } from '@angular/material/datepicker';

@Component({
    standalone: true,
    imports: [ApptSearchListComponent, ApptSearchFormComponent, FillHeightDirective, MaterialPackage],
    templateUrl: './appt-list.component.html',
    styleUrl: './appt-list.component.scss'
})
export class VisitMgmtApptListComponent {
    fromDate:Date;
    toDate:Date;
    appList: any[]=[];
    patient: any;
    appoinmentDetails: any;
    appointmentdata: any;
    filterValue:any[] = [];
    constructor(
        private _HISAPI: AppointmentsAPIService,
        private router: Router,
        private route: ActivatedRoute,
        public commonServices: CommonService,
        public _appService: ApplicationService
    ) {
        this.toDate = dateUtils.truncTime(new Date());
        this.fromDate = dateUtils.addMonths(this.toDate,-1);
     }

    ngOnInit(): void {
        const startDate = dateUtils.toISODate(this.fromDate);
        const endDate = dateUtils.toISODate(this.toDate);
        this._HISAPI.appointmentsAPIGetAppointmentQueueGet({fromDate:startDate, toDate:endDate}).subscribe((data:any[]) => {
            this.appList = data || [];
        })
    }

    onSearch(data:any) {
            const startDate = dateUtils.toISODate(data.fromDate);
            const endDate = dateUtils.toISODate(data.toDate);

            this._HISAPI.appointmentsAPIGetAppointmentQueueGet({
                fromDate:startDate, 
                toDate:endDate, 
                apptNo:data?.AptNo, 
                idType:data?.IdType, 
                idNo:data?.IdNo
                        }).subscribe((data) => {
                if (data) {
                    this.appList = data;
                    if (this.appList.length == 0) {
                        this._appService.alertDialog({
                            'title': 'System Error',
                            message: 'No Records Found.'
                        });
                    }
                }
            });
    }


    onCheck(data:any) {
        this.router.navigate(['register', { IDENTIFIER: data.IDENTIFIER, INDIVIDUALID: data.INDIVIDUALID }], { relativeTo: this.route })
    }
}



