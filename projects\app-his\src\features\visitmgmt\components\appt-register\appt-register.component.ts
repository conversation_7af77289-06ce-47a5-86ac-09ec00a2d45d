import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { EcmedWebapiModelsPatientmgmtSearchPatientType, PatientService, VisitService }
  from 'ecmed-api/visitmgmt';
import {
  AppointmentViewComponent, CommonService,
  PatientFormComponent, PatientIndividualFormComponent, VisitPayerListComponent
} from 'his-components';
import { QueuePatientRegisterComponent, ArPayDialogComponent } from '@app-his/components';
import { Location } from '@angular/common'
import { MaterialPackage } from '@app-his/utils';
import { BillingService } from 'ecmed-api/billing';
import { ApplicationService } from 'ec-ngcore';
import { FillHeightDirective, SearchLayoutComponent } from 'ec-ngcore/ui';

@Component({
  standalone: true,
  imports: [PatientFormComponent, FillHeightDirective, SearchLayoutComponent,
    MatIconModule, MatButtonModule, MatCardModule, QueuePatientRegisterComponent,
    MaterialPackage],
  templateUrl: './appt-register.component.html',
  styleUrl: './appt-register.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class VisitMgmtApptRegisterComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    public commonServices: CommonService,
    private router: Router,
    public _HISAPI: AppointmentsAPIService,
    private _visitAPi: VisitService,
    private _Api: PatientService,
    public _dialog: MatDialog,
    public location: Location,
    private _appService: ApplicationService,
    public api: BillingService,


  ) { }
  ngOnInit(): void {

    this.route.paramMap.subscribe((paramMap) => {
      const identifier = paramMap.get('IDENTIFIER');
      const individualId = paramMap.get('INDIVIDUALID');
      if (identifier && individualId) {
        this.patientId = individualId;
        this.patientappoinmentId = identifier;
      }
    });
    this.patientData();
    this.patientAppoinmentDetails();
    const codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
    this.commonServices.fetchVisitData(codeTypes).subscribe(
      (response: any) => {
        this.visitData = response;
      },

    );
    this.handleGetPayerDetails(this.paitentidno)
  }

  @ViewChild(PatientFormComponent) patientFormComponent?: PatientFormComponent;
  payerDetails: any = [];
  dupPayerDetails: any = []
  buttonLabel: string = "Finalize";
  page: any = "manualVisit";
  isLoading: boolean = false;
  patientId: any;
  patientappoinmentId: any;
  patient: any;
  visitData: any = {};
  data: any;
  searchResults: object[] = [];
  appointmentdata: any;
  inputData?: object | object[];
  savedindividualresult?: object;
  patientdata: object[] = [];
  patients: any[] = [];
  visitADD: any;
  visitid?: string;
  values: any
  visitcreate: any;
  editPatModelsave: any
  paitentidno: any;
  activeTab: string = 'home';



  goBack() {
    this.location.back()
  }
  savePatient() {
    this.patientFormComponent?.handleSaveUpdatePatient('save');
  }

  updatePatient() {
    this.patientFormComponent?.handleSaveUpdatePatient('update')
  }

  patientData(): void {
    this.isLoading = true;
    this._Api.patientGetIndividualPatDetailGet({id:this.patientId}).subscribe({
      next: (detailResult: any) => {
        if (detailResult && !detailResult.error) {
          this.data = detailResult;
          this.paitentidno = detailResult?.Detail[0]?.IDENTIFIER
          this.handleGetPayerDetails(this.paitentidno)
          if (this.data.Detail?.length > 0 && this.data.Detail[0].HRN) {
            this.buttonLabel = "Finalize";
          } else {
            this.buttonLabel = "Save";
            this.fetchPatientDetailsFallback();
          }
        } else {
          this.fetchPatientDetailsFallback();
        }
      },
      error: (err) => {
        // Directly call fallback function on network or server errors
        this.fetchPatientDetailsFallback();
      },
      complete: () => {
        this.isLoading = false;
      }
    });
  }
  // onButtonClicked() {
  //   console.log(this.data.Detail[0].HRN,'this.data.Detail[0].HRN')
  //   if (this.data.Detail[0].HRN) {
  //     this.updatePatient();
  //   } else {
  //     this.savePatient();
  //   }
  // }

  onButtonClicked() {

    if (this.data.Detail[0] && this.data.Detail[0].HRN) {
      // HRN is present, so update the patient
      this.updatePatient();
    } else {
      // HRN is not present, so save a new patient
      this.savePatient();
    }
  }
  private fetchPatientDetailsFallback(): void {

    this._appService.alertDialog({
      'title': 'System Error',
      message: 'This is Individual Details Convert it into Patient and Save'
    });

    this._Api.patientGetIndividualDetailGet(this.patientId).subscribe({
      next: (detailResult) => {
        this.patient = detailResult;
        this.convertPatientDataToFormat(this.patient);
      }
    });
  }


  convertPatientDataToFormat(patient: any[]) {
    const item = patient[0];
    const formattedData = {
      "Detail": [
        {
          "IDENTIFIER": item.IDENTIFIER,
          "_ORGCODE": item._ORGCODE,
          "HRN": item.HRN,
          "IDNO": item.IDNO,
          "IDTYPE": item.IDTYPE,
          "NAME": item.NAME,
          "MERGED": item.MERGED,
          "SEX": item.GENDER,
          "RACE": item.ETHNICITY,
          "DOB": item.DOB,
          "RESIDENCE_CODE": item.RESIDENCE_CODE,
          "EMAIL": item.EMAIL,
          "ALERTEMAIL": item.ALERTEMAIL,
          "ALERTSMS": item.ALERTSMS,
          "ORGANDONOR": item.ORGANDONOR,
          "BLOODGROUP": item.BLOODGROUP,
          "NATIONALITY": item.NATIONALITY,
          "PLACE_OF_BIRTH": item.PLACE_OF_BIRTH,
          "VIPSTATUS": item.VIPSTATUS,
          "LANGUAGECODE": item.LANGUAGECODE,
          "TITLECODE": item.TITLE,
          "REMARKS": item.REMARKS,
          "EDUCATION": item.EDUCATION,
          "OCCUPATION": item.OCCUPATION,
          "RELIGION": item.RELIGION,
          "PATIENTTYPE": item.PATIENTTYPE,
          "MARITALSTATUS": item.MARITALSTATUS,
        }
      ],
      "NextOfKin": [
        {
          "IDENTIFIER": item.IDENTIFIER,
          "PATIENTID": item.PATIENTID,
          "RELATIONSHIPCODE": item.RELATIONSHIPCODE,
        }
      ],
      "PatientAddress": [
        {
          "ADDRESSTYPE": item.ADDRESSTYPE,
          "ADDRESSTYPE_DESC": item.ADDTYPE_DESC,
          "ISDEFAULT": item.ISDEFAULT,
          "TEL": item.TEL,
          "TEL2": item.TEL2,
          "TEL3": item.TEL3,
          "TEL4": item.TEL4,
          "ADDRESS1": item.ADDRESS1,
          "ADDRESS2": item.ADDRESS2,
          "ADDRESS3": item.ADDRESS3,
          "ADDRESS4": item.ADDRESS4,
          "CITY": item.CITY,
          "STATE": item.STATE,
          "COUNTRY": item.COUNTRY,
          "COUNTRYDESC": item.COUNTRY_DESC,
          "POSTALCODE": item.POSTALCODE
        }
      ]
    }
    this.data = formattedData
    return formattedData;
  }

  handleSave(event: any) {
    this.editPatModelsave = event;
    this.VisitFinalize();
  }

  handleedit(event: any) {
    this.editPatModelsave = event;
    this.VisitFinalize()
  }

  onDateChanged(dateOfBirth: any): void {
    const dateOfBirthString: string = dateOfBirth.toISOString();
    this.commonServices.calculateAge(dateOfBirthString);
  }

  handleSearchData(searchData: any) {
    if (!searchData || !searchData.data) {
      return;
    }


    const { searchType, name, idNo, idType } = searchData.data;
    this._Api.patientSearchIndividualGet({
      searchType: searchType as EcmedWebapiModelsPatientmgmtSearchPatientType,
      name: name,
      iDNo: idNo,
      iDType: idType,
    }).subscribe(
      (results) => {
        this.searchResults = results;
        if (this.searchResults.length === 0) {
          this.openConfirmDialog();
        } else {
          this.searchResults = results;
        }
      }
    );
  }

  handleEventData(eventData: any): void {
    if (eventData.action === 'search') {
      this.handleSearchData(eventData);
    } else if (eventData.action === 'edit') {
      this.handleEditData(eventData);
    }
  }

  handleEditData(searchData: any) {
    if (!searchData || !searchData.data) {
      return;
    }
    const { searchType, name, idNo, idType } = searchData.data;
    const identifier = searchData.data.identifier;
    if (identifier) {
      this.openeditDialog(identifier);
    } else {
      this._Api.patientSearchIndividualGet(searchType, name, idNo, idType).subscribe(
        (results: any) => {
          if (results && results.length > 0) {
            const identifierFromResults = (results[0] as any).IDENTIFIER;
            if (identifierFromResults) {
              this.openeditDialog(identifierFromResults);
            }
          }
        }
      );
    }
  }

  openConfirmDialog(): void {
    this._appService.confirmDialog({ message: 'Unable to find the patient. Do you want create new patient?' }).afterClosed().subscribe(result => {
      if (result == 'Yes') {
        this.openDialog();
      }
    });
  }

  openDialog() {
    const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.fetchPatientDetails(result);
      }
    });
  }

  openeditDialog(identifier: string) {
    const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
      data: identifier
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.fetchPatientDetails(result);
      }
    });
  }

  fetchPatientDetails(id: string): void {
    this._Api.patientGetIndividualDetailGet({ id: id }).subscribe(
      (data: object | object[]) => {
        const patientDetails = Array.isArray(data) ? (data[0] as object) : (data as object);
        if (patientDetails) {
          this.savedindividualresult = patientDetails;
        }
      }
    );
  }

  patientsearch(patient: any) {
    let searchType: EcmedWebapiModelsPatientmgmtSearchPatientType = 'All';
    if (patient.name && patient.id && patient.HRN && patient.idType) {
      searchType = EcmedWebapiModelsPatientmgmtSearchPatientType.All;
    }
    else if (patient.name) {
      searchType = EcmedWebapiModelsPatientmgmtSearchPatientType.ByName;
    }

    else if (patient.id && patient.idType) {
      searchType = EcmedWebapiModelsPatientmgmtSearchPatientType.ByIdNo;
    }
    else if (patient.HRN) {
      searchType = EcmedWebapiModelsPatientmgmtSearchPatientType.ByHrn;

    }
    this._Api.patientSearchGet(
      {
        searchType: searchType, name: patient.name, sex: patient.Sex,
        hRN: patient.HRN, iDNo: patient.id, iDType: patient.idType
      }).subscribe((result) => {
        if (result) {
          this.patientdata = result;
        }
        else {
          this.patients = [];
          window.alert('no records found ')
        }
      }
      );
  }

  patientAppoinmentDetails() {
    this._HISAPI.appointmentsAPIGetAppointmentDetailsGet({ id: this.patientappoinmentId }).subscribe(
      (detailResult) => {
        this.appointmentdata = detailResult;
      }
    );
  }

  backbutton() {
    this.router.navigate(['his-psd/visits'])
  }

  public VisitFinalize(): void {
    let visitdata = {
      "Identifier": "",
      "VisitNo": null,
      "VisitDate": this.appointmentdata[0]?.SCHEDULEDATETIME || new Date().toISOString(),
      "PatientId": this.visitcreate || '',
      "Discharged": null,
      "Status": null,
      "StatusDate": null,
      "ReferredBy": null,
      "SpecialityId": this.appointmentdata[0]?.SPECIALITYCODE || null,
      "ClinicId": this.appointmentdata ? this.appointmentdata[0].CLINICCODE.toString() || null : null,
      "WardNo": this.visitADD ? this.visitADD.WardNo?.toString() || null : null,
      "BedNo": this.visitADD ? this.visitADD.BedNo?.toString() || null : null,
      "IsAmbulance": this.visitADD ? this.visitADD.IsAmbulance || null : null,
      "IsPoliceCase": this.visitADD ? this.visitADD.IsPoliceCase?.toString() || null : null,
      "PlaceOfAccident": this.visitADD ? this.visitADD.PlaceOfAccident?.toString() || null : null,
      "Remarks": this.visitADD ? this.visitADD.Remarks?.toString() || null : null,
      "VisitType": this.visitADD ? this.visitADD.VisitType?.toString() || null : null,
      "VisitReason": this.visitADD ? this.visitADD.VisitReason?.toString() || null : null,
      "DischargeType": this.visitADD ? this.visitADD.DischargeType?.toString() || null : null,
      "DischargeDate": null,
      "AdmissionReasonCode": this.visitADD ? this.visitADD.AdmissionReasonCode?.toString() || null : null,
      "DischargeReasonCode": this.visitADD ? this.visitADD.DischargeReasonCode?.toString() || null : null,
      "Resus": this.visitADD ? this.visitADD.Resus || null : null,
      "Staff": this.visitADD ? this.visitADD.Staff || null : null,
      "EpisodeVisitNo": this.visitADD ? this.visitADD.EpisodeVisitNo?.toString() || null : null,
      "AppointmentNo": this.appointmentdata[0]?.IDENTIFIER?.toString() || null,
      "Consultation": this.visitADD ? this.visitADD.Consultation?.toString() || null : null,
      "WalkInPatient": this.visitADD ? this.visitADD.WalkInPatient?.toString() || null : null,
      "FinancialClass": this.visitADD ? this.visitADD.FinancialClass || null : null,
      "ReferralSpecialityCode": this.visitADD ? this.visitADD.ReferralSpecialityCode?.toString() || null : null,
      "ReferredDoctorId": this.visitADD ? this.visitADD.ReferredDoctorId?.toString() || null : null,
      "ReferralCode": this.visitADD ? this.visitADD.ReferralCode?.toString() || null : null,
      "CaseSheetAvailable": this.visitADD ? this.visitADD.CaseSheetAvailable?.toString() || null : null,
      "AppointmentRemarks": this.visitADD ? this.visitADD.AppointmentRemarks?.toString() || null : null,
      "ResourceCode": this.visitADD ? this.visitADD.ResourceCode?.toString() || null : null,
      "AssignedDoctorCode": this.appointmentdata[0]?.ASSIGNEDDOCTOR?.toString() || null,
      "AttendingDoctor": this.appointmentdata[0]?.ATTENDINGDOCTOR?.toString() || null,
      "PerformedDoctorCode": this.visitADD ? this.visitADD.PerformedDoctorCode?.toString() || null : null,
      "AssignedRoomCode": this.visitADD ? this.visitADD.AssignedRoomCode?.toString() || null : null,
      "PerformedRoomCode": this.visitADD ? this.visitADD.PerformedRoomCode?.toString() || null : null,
      "EquipmentCode": this.visitADD ? this.visitADD.EquipmentCode?.toString() || null : null,
      "SubSpecialityCode": this.visitADD ? this.visitADD.SubSpecialityCode?.toString() || null : null,
      "CancelIndicator": this.visitADD ? this.visitADD.CancelIndicator?.toString() || null : null,
      "CancelledBy": this.visitADD ? this.visitADD.CancelledBy?.toString() || null : null,
      "CanclledDate": null,
      "Recompensable": this.visitADD ? this.visitADD.Recompensable?.toString() || null : null,
      "DowntimeIndicator": this.visitADD ? this.visitADD.DowntimeIndicator?.toString() || null : null,
      "IAIndicator": this.visitADD ? this.visitADD.IAIndicator?.toString() || null : null,
      "NameReferralIndicator": this.visitADD ? this.visitADD.NameReferralIndicator?.toString() || null : null,
      "PatientRequestIndicator": this.visitADD ? this.visitADD.PatientRequestIndicator?.toString() || null : null,
      "GPCode": this.visitADD ? this.visitADD.GPCode?.toString() || null : null,
      "LocationCode": this.visitADD ? this.visitADD.LocationCode?.toString() || null : null,
      "editPatModel": JSON.parse(this.editPatModelsave)
    }
    this._visitAPi.visitCreateVisitPost({ id: this.patientId, ecmedWebapiModelsVisitmgmtEditVisitDataModel: visitdata }).subscribe((data) => {
      if (data) {
        this.visitid = data;
      }
      this.router.navigate(['his-psd/appointmentqueue/view/' + this.visitid]);
    });
  }



  public Visitupdate(): void {
    let visitdata = {
      "Identifier": null,
      "_OrgCode": 0,
      "VisitNo": null,
      "VisitDate": this.appointmentdata[0]?.SCHEDULEDATETIME || new Date().toISOString(),
      "PatientId": this.visitcreate,
      "Discharged": '',
      "Status": null,
      "StatusDate": null,
      "ReferredBy": null,
      "SpecialityId": this.appointmentdata[0]?.SPECIALITYCODE || null,
      "ClinicId": this.appointmentdata[0]?.CLINIC_ID || null,
      "WardNo": this.visitADD ? this.visitADD.WardNo?.toString() || null : null,
      "BedNo": this.visitADD ? this.visitADD.BedNo?.toString() || null : null,
      "IsAmbulance": this.visitADD ? this.visitADD.IsAmbulance || null : null,
      "IsPoliceCase": this.visitADD ? this.visitADD.IsPoliceCase?.toString() || null : null,
      "PlaceOfAccident": this.visitADD ? this.visitADD.PlaceOfAccident?.toString() || null : null,
      "Remarks": this.visitADD ? this.visitADD.Remarks?.toString() || null : null,
      "VisitType": this.visitADD ? this.visitADD.VisitType?.toString() || null : null,
      "VisitReason": this.visitADD ? this.visitADD.VisitReason?.toString() || null : null,
      "DischargeType": this.visitADD ? this.visitADD.DischargeType?.toString() || null : null,
      "DischargeDate": null,
      "AdmissionReasonCode": this.visitADD ? this.visitADD.AdmissionReasonCode?.toString() || null : null,
      "DischargeReasonCode": this.visitADD ? this.visitADD.DischargeReasonCode?.toString() || null : null,
      "Resus": this.visitADD ? this.visitADD.Resus || null : null,
      "Staff": this.visitADD ? this.visitADD.Staff || null : null,
      "EpisodeVisitNo": this.visitADD ? this.visitADD.EpisodeVisitNo?.toString() || null : null,
      "AppointmentNo": this.appointmentdata[0]?.IDENTIFIER?.toString() || null,
      "Consultation": this.visitADD ? this.visitADD.Consultation?.toString() || null : null,
      "WalkInPatient": this.visitADD ? this.visitADD.WalkInPatient?.toString() || null : null,
      "FinancialClass": this.visitADD ? this.visitADD.FinancialClass || null : null,
      "ReferralSpecialityCode": this.visitADD ? this.visitADD.ReferralSpecialityCode?.toString() || null : null,
      "ReferredDoctorId": this.visitADD ? this.visitADD.ReferredDoctorId?.toString() || null : null,
      "ReferralCode": this.visitADD ? this.visitADD.ReferralCode?.toString() || null : null,
      "CaseSheetAvailable": this.visitADD ? this.visitADD.CaseSheetAvailable?.toString() || null : null,
      "AppointmentRemarks": this.visitADD ? this.visitADD.AppointmentRemarks?.toString() || null : null,
      "ResourceCode": this.visitADD ? this.visitADD.ResourceCode?.toString() || null : null,
      "AssignedDoctorCode": this.visitADD ? this.visitADD.AssignedDoctorCode?.toString() || null : null,
      "AttendingDoctor": this.visitADD ? this.visitADD.AttendingDoctor?.toString() || null : null,
      "PerformedDoctorCode": this.visitADD ? this.visitADD.PerformedDoctorCode?.toString() || null : null,
      "AssignedRoomCode": this.visitADD ? this.visitADD.AssignedRoomCode?.toString() || null : null,
      "PerformedRoomCode": this.visitADD ? this.visitADD.PerformedRoomCode?.toString() || null : null,
      "EquipmentCode": this.visitADD ? this.visitADD.EquipmentCode?.toString() || null : null,
      "SubSpecialityCode": this.visitADD ? this.visitADD.SubSpecialityCode?.toString() || null : null,
      "CancelIndicator": this.visitADD ? this.visitADD.CancelIndicator?.toString() || null : null,
      "CancelledBy": this.visitADD ? this.visitADD.CancelledBy?.toString() || null : null,
      "CanclledDate": null,
      "Recompensable": this.visitADD ? this.visitADD.Recompensable?.toString() || null : null,
      "DowntimeIndicator": this.visitADD ? this.visitADD.DowntimeIndicator?.toString() || null : null,
      "IAIndicator": this.visitADD ? this.visitADD.IAIndicator?.toString() || null : null,
      "NameReferralIndicator": this.visitADD ? this.visitADD.NameReferralIndicator?.toString() || null : null,
      "PatientRequestIndicator": this.visitADD ? this.visitADD.PatientRequestIndicator?.toString() || null : null,
      "GPCode": this.visitADD ? this.visitADD.GPCode?.toString() || null : null,
      "LocationCode": this.visitADD ? this.visitADD.LocationCode?.toString() || null : null,
      "editPatModel": JSON.parse(this.editPatModelsave)
    }
    this._visitAPi.visitUpdateVisitPost({ ecmedWebapiModelsVisitmgmtEditVisitDataModel: visitdata }).subscribe((data) => {
      if (data) {
        this.visitid = data;
      }
      this.router.navigate(['his-psd/appointmentqueue/view/' + this.visitid]);
    });
  }



  public handleGetPayerDetails(data: any) {
    this.api.billingGetPayerDetailssGet({ id: data }).subscribe(res => {
      this.payerDetails = res
      this.dupPayerDetails = res

    })
  }
  public handleThirdPartyPayer(element: any) {
    ArPayDialogComponent.openDialog(this._dialog, {
      // visitId: this.dialogData['visitId'],
      patId: this.paitentidno,
      dataObj: element
    }).afterClosed().subscribe(res => {
      this.handleGetPayerDetails(this.paitentidno)
    })
  }


  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}
