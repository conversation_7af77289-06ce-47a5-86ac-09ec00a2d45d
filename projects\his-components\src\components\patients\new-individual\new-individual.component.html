<div class="newIndividualComponent w-full h-full">
    <div class="flex justify-between items-center mb-2">
        <span>Individual Details</span>
        <span class="flex justify-between items-center gap-x-2 ml-auto">
            <button mat-flat-button color="warn" (click)="handleReset()">Reset</button>
            <button mat-flat-button color="primary" type="submit" (click)="handleSubmit()">Finalize</button>
        </span>
    </div>
    <mat-divider></mat-divider>
    <div class="flex items-center justify-between gap-x-2 mt-2">
        <mat-form-field #idTypeInput appearance="outline" class="w-full">
            <mat-label>ID Type</mat-label>
            <mat-select required [(ngModel)]="patientDetails.IDType">
                <mat-option *ngFor="let data of visitData?.ID" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                    }}</mat-option> </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
            <mat-label>IDNO</mat-label>
            <input matInput placeholder="IDNO" name="patientName" required [(ngModel)]="patientDetails.IDNo">
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
            <mat-label>HRN</mat-label>
            <input matInput placeholder="IDNO" disabled required name="HRN">
        </mat-form-field>

    </div>

    <div class="flex items-center justify-between gap-x-2">
        <mat-form-field appearance="outline" class="w-full">
            <mat-label>Title</mat-label>
            <mat-select [(ngModel)]="patientDetails.Title">
                <mat-option *ngFor="let data of visitData?.TC" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                    }}</mat-option> </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
            <mat-label for="label">Name</mat-label>
            <input matInput type="text" required aria-describedby="emailHelp" maxlength="100" autocomplete="off"
                [(ngModel)]="patientDetails.Name">
        </mat-form-field>
    </div>

    <div class="flex items-center justify-between gap-x-2">
        <mat-form-field appearance="outline" class="w-full">
            <mat-label for="label">Gender</mat-label>
            <mat-select required [(ngModel)]="patientDetails.Gender">
                <mat-option *ngFor="let data of visitData?.SX" [value]="data.IDENTIFIER">
                    {{ data.DESCRIPTION }}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
            <mat-label for="label">DOB</mat-label>
            <input ec-AutoFormatDate [(ngModel)]="patientDetails.DateOfBirth"
                (dateChange)="handleOnChange('dob',$event)" matInput [matDatepicker]="picker" required
                placeholder="MM/DD/YYYY">
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
            <mat-label for="label">Age</mat-label>
            <input [value]="age" matInput type="number" aria-describedby="emailHelp" maxlength="100" autocomplete="off"
                readonly>
        </mat-form-field>
    </div>

    <mat-tab-group>
        <mat-tab>
            <ng-template mat-tab-label class="address-icon">
                <mat-icon svgIcon="assignment_ind" class="address-icon"></mat-icon>
                <strong class="address-icon"> Personal Profile</strong>
            </ng-template>
            <mat-divider></mat-divider>
            <div class="flex items-center justify-between gap-x-2 mt-2">
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Ethnicity</mat-label>
                    <mat-select [(ngModel)]="patientDetails.Ethnicity">
                        <mat-option *ngFor="let data of visitData?.RC"
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>

                    </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Place of Birth</mat-label>
                    <mat-select [(ngModel)]="patientDetails.PlaceOfBirth">
                        <mat-option *ngFor="let data of visitData?.NA "
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>

                    </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Nationality</mat-label>
                    <mat-select [(ngModel)]="patientDetails.Nationality">
                        <mat-option *ngFor="let data of visitData?.NA "
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label for="label">Email</mat-label>
                    <input [(ngModel)]="patientDetails.Email" matInput type="email" name="email" required email
                        aria-describedby="emailHelp" maxlength="100" autocomplete="off">
                </mat-form-field>

            </div>

            <div class="flex items-center justify-between gap-x-2">

                <mat-form-field class="w-full" appearance="outline">
                    <mat-label>Religion</mat-label>
                    <mat-select [(ngModel)]="patientDetails.Religion">
                        <mat-option *ngFor="let data of visitData?.RE" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Language Spoken</mat-label>
                    <mat-select [(ngModel)]="patientDetails.LanguageCode">
                        <mat-option *ngFor="let data of visitData?.LG"
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
                    </mat-select>
                </mat-form-field>


                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Education</mat-label>
                    <mat-select [(ngModel)]="patientDetails.Education">
                        <mat-option *ngFor="let data of  visitData?.ED"
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
                    </mat-select>
                </mat-form-field>


                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Occupation</mat-label>
                    <mat-select [(ngModel)]="patientDetails.Occupation">
                        <mat-option *ngFor="let data of visitData?.ON"
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>Marital Status</mat-label>
                    <mat-select [(ngModel)]="patientDetails.MaritalStatus">
                        <mat-option *ngFor="let data of visitData?.MS "
                            [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>

            <div class="flex items-center justify-start gap-x-2">
                <mat-checkbox class="example-margin" [checked]="
                this.patientDetails.OrganDonor === 'Y'
            " (change)="onCheckboxOrganDonor($event)">Organ
                    Donor</mat-checkbox>
                <mat-checkbox class="example-margin" [checked]="
                this.patientDetails.SpeakEnglish === 'Y'
            " (change)="onCheckboxSpeakEnglish($event)">Can Speak
                    English</mat-checkbox>
                <span class="font-semibold">AlertType</span>
                <mat-checkbox class="example-margin" [checked]="
                this.patientDetails.AlertEmail === 'Y'
            " (change)="onCheckboxAlertEmail($event)">Email</mat-checkbox>
                <mat-checkbox class="example-margin" [checked]="this.patientDetails.AlertSMS === 'Y'"
                    (change)="onCheckboxAlertSMS($event)" name="AlertSMS">SMS</mat-checkbox>
            </div>


        </mat-tab>
        <mat-tab>
            <ng-template mat-tab-label class="address-icon">
                <mat-icon svgIcon="assignment" class="address-icon"></mat-icon>
                <strong class="address-icon">Address</strong>
            </ng-template>

            <div class="pt-2 w-100">
                <!-- Use his-common-address component -->
                <his-common-address
                    [addressData]="getAddressData()"
                    [visitData]="visitData"
                    [phoneForm]="myForm"
                    (addressChange)="onAddressChange($event)">
                </his-common-address>
            </div>
        </mat-tab>
    </mat-tab-group>


</div>