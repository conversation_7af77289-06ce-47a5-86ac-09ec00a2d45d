import { CommonModule, DatePipe } from '@angular/common';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Component, EventEmitter, Output,Input, OnInit } from '@angular/core';
import { FormsModule, FormGroup, FormControl } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { Observable, of, switchMap } from 'rxjs';
import {CommonService, SearchPatientDlg} from 'his-components';
import { MatDialog } from '@angular/material/dialog';
import { MaterialPackage} from '@app-his/utils';
import { dateUtils } from 'ec-ngcore';
import { VisitService } from 'ecmed-api/visitmgmt';
@Component({
  selector: 'app-visit-searchform',
  standalone: true,
  imports: [CommonModule, MaterialPackage, MatSnackBarModule, FormsModule, MatTableModule],
  templateUrl: './search-form.component.html',
  styleUrl: './search-form.component.scss'
})
export class VisitSearchFormComponent  implements OnInit{
  @Input() fromDate:Date|null=null;
  @Input() toDate:Date|null=null;
  @Input() showSearchPatientDlg:boolean=true;
  @Input() enableAdd:boolean=false;

  @Output() onNew: EventEmitter<any> = new EventEmitter<any>();
  @Output() onSearch: EventEmitter<any> = new EventEmitter<any>();
  @Output() onReset: EventEmitter<void> = new EventEmitter<void>();
  @Output() onSearchPatient:EventEmitter<void>=new EventEmitter<void>();
  @Input() enableSearchPatient:boolean=false;
  codeTypes:any;

  constructor(
    private _dialog:MatDialog,
    private visitApi:VisitService,
    private commonServices:CommonService)
    {
      this.commonServices.fetchVisitData(['ID']).subscribe((result:any) => {this.codeTypes = result;});
    }

  searchTerm: VisitSearch ={
    idNo: '',
    idType: '',
    name: '',
    visitNo:'',
    hrn:'',
  }

  ngOnInit()
  {
  }

  get HRN():string {return this.searchTerm.hrn;}
  set HRN(value:string) {this.searchTerm.hrn = value;}
  onFormReset(): void {
    this.onReset.emit();
  }
  onFormSubmit(): void {
    let ev = Object.assign({fromDate: this.fromDate, toDate:this.toDate},this.searchTerm);
    this.onSearch.emit(ev);
  }

   searchPatient(){
    this.onSearchPatient.emit();
    if (this.showSearchPatientDlg){
       SearchPatientDlg.openDialog(this._dialog, {}).afterClosed().subscribe((result)=>
        { if (result) this.searchTerm.hrn = result.HRN; this.searchTerm.idNo = result.IDNO;this.searchTerm.idType = result.IDTYPE; });;
    }
   } 

  searchVisits():Observable<any>{
    // const apiparams = new HttpParams().append("HRN", this.searchTerm.hrn)
    //        .append("fromDate",dateUtils.toISODate(this.fromDate) ||  '')
    //        .append("toDate",dateUtils.toISODate(this.toDate) || '')
    //        .append("IDNO",this.searchTerm.idNo)
    //        .append("IDType",this.searchTerm.idType)
    //        .append("VisitNo",this.searchTerm.visitNo);
return this.visitApi.visitSearchVisitsGet({hRN:this.searchTerm.hrn,
    fromDate:dateUtils.toISODate(this.fromDate) ||  '',
    toDate:dateUtils.toISODate(this.toDate) || '',
    iDNo:this.searchTerm.idNo,
    // IDType:this.searchTerm.idType,
    visitNo:this.searchTerm.visitNo
  })
    //  return this._api.get('/api/visitmgmt/Visit/SearchVisits', 
    //                        {params:apiparams}).pipe(switchMap((response:any)=>
    //                         { 
    //                           return of(response)}
    //                         ));
  }
  doAdd(){this.onNew.emit();}
}

export interface VisitSearch{
  idNo: string
  idType: string,
  name: string,
  visitNo:string,
  hrn:string,
}

