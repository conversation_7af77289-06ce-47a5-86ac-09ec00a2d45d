import { HttpErrorResponse, HttpEvent, Http<PERSON><PERSON><PERSON>, HttpHandlerFn, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, EMPTY, Observable, throwError } from 'rxjs';
import { ApplicationService } from './application.service';
import { EC_CORE_HTTPCONTEXT_TOKEN } from './http-context'
import { Router } from '@angular/router';

/**
 * Intercept
 *
 * @param req
 * @param next
 */
@Injectable()
export class authInterceptor implements HttpInterceptor{
 constructor(private router: Router /*, private authService: AuthService */) {}
  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const appService = inject(ApplicationService);
    console.log('auth.interceptor:', request.url);

    // Clone the request object
    let newReq = request;
    const ctx = request.context.get(EC_CORE_HTTPCONTEXT_TOKEN);
    // Request
    //
    // If the access token didn't expire, add the Authorization header.
    // We won't add the Authorization header if the access token expired.
    // This will force the server to return a "401 Unauthorized" response
    // for the protected API routes which our response interceptor will
    // catch and delete the access token from the local storage while logging
    // the user out from the app.
    if ( appService.authService.tokenValid )
    {
        newReq = request.clone({
            headers: request.headers.set('Authorization', 'Bearer ' + appService.authService.accessToken),
        });
    }

    // Response
    return next.handle(newReq).pipe(
        catchError((error:HttpErrorResponse) =>
        {
            // Catch "401 Unauthorized" responses
            if ( error && error.status === 401 && appService.isBrowserMode )
            {
                if (ctx && (
                          (ctx.ignoreAll ?? false)  || 
                          (ctx.ignoreCodes?.includes('401'))))
                   return EMPTY;
                // Sign out
                appService.authService.signOut();
                this.router.navigate(['/sign-in']);
                // Reload the app
                //location.reload();
            }

            return throwError(error);
        }),
    );

  }
} 
