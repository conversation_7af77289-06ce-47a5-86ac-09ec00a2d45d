<form class="form-inline p-2" novalidate (ngSubmit)="onSubmit()">
    <div class="flex flex-row bg-blue-600">
        <div class="flex-grow-1 pt-2 pl-2 text-2xl font-bold text-white">
            Individual Details
        </div>
        <div class="pt-2 ml-auto">
            <button mat-stroked-button (click)="goback()" class="bg-white mr-1 mb-3 text-red-600">
                <mat-icon class="icon-size-5 mr-2 text-red-600" [svgIcon]="'mat_outline:delete'"></mat-icon>
                Discard
            </button>
            <button mat-stroked-button type="submit" class="bg-white mr-1 mb-3 text-green-600">
                <mat-icon class="icon-size-5 mr-2 text-green-600" [svgIcon]="'mat_outline:save_alt'"></mat-icon>
                Finalize
            </button>
        </div>
    </div>
    <mat-card class="shadow-none bg-white">
        <mat-card-content>
            <div class="row">
                <div class="col-12">
                    <div class="range">
                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label for="label"><strong>ID</strong></mat-label>
                            <input matInput type="text" [(ngModel)]="patientDetails.IDNo" id="idno" required idno
                                name="idno" aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
                            @if (!patientDetails.IDNo || patientDetails.IDNo.length > 100){
                            <mat-error>IDNo is required.</mat-error>
                            }
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label for="label">IDTYPE</mat-label>
                            <mat-select [(ngModel)]="patientDetails.IDType" name="idtype">
                                @for(data of visitData?.ID; track $index;){
                                <mat-option [value]="data.IDENTIFIER">
                                    {{data.DESCRIPTION }}
                                </mat-option>
                                }
                            </mat-select>
                            @if(!patientDetails.IDType){
                            <mat-error>
                                IDType is required.
                            </mat-error>
                            }
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label>Title</mat-label>
                            <mat-select [(ngModel)]="patientDetails.Title" name="Title">
                                @for(data of visitData?.TC; track $index){
                                <mat-option [value]="data.IDENTIFIER">
                                    {{data.DESCRIPTION }}</mat-option>

                                }
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label for="label"><strong>Name</strong></mat-label>
                            <input matInput type="text" [(ngModel)]="patientDetails.Name" id="name" required name
                                name="Name" aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
                            @if (!patientDetails.Name ||patientDetails.Name.length > 100){
                            <mat-error>
                                Name is required.
                            </mat-error>

                            }
                        </mat-form-field>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-9">
                    <div class="range">
                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label for="label">Gender</mat-label>
                            <mat-select [(ngModel)]="patientDetails.Gender" id="gender" required gender name="Gender">
                                @for (data of visitData?.SX; track $index;){
                                <mat-option [value]="data.IDENTIFIER">
                                    {{ data.DESCRIPTION }}
                                </mat-option>

                                }
                            </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="inputbox">
                            <mat-label for="label">DOB</mat-label>

                            <input matInput ec-AutoFormatDate [matDatepicker]="picker"
                                [(ngModel)]="patientDetails.DateOfBirth" name="DateOfBirth" required (dateChange)="
                                    calculateAgeFromDOB(patientDetails.DateOfBirth)
                                " [max]="today" placeholder="MM/DD/YYYY" />
                            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                            <mat-datepicker #picker></mat-datepicker>
                            @if (!patientDetails.DateOfBirth){
                            <mat-error> DateOfBirth is required. </mat-error>
                            }
                        </mat-form-field>

                        <mat-form-field appearance="outline" class="inputbox">
                            <input matInput type="number" [value]="
                                    calculateAgeFromDOB(
                                        patientDetails.DateOfBirth
                                    )
                                " readonly placeholder="Age" />
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </mat-card-content>
    </mat-card>
    <mat-card class="shadow-none bg-white w-full">
        <mat-tab-group>
            <mat-tab>
                <ng-template mat-tab-label>
                    <mat-icon svgIcon="assignment_ind" class="text-blue-500"></mat-icon>
                    <strong class="text-blue-500"> Personal Profile</strong>
                </ng-template>
                <div class="flex pt-2 w-full">
                    <div class="col-sm-12">
                        <div class="range">
                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Ethnicity</mat-label>
                                <mat-select [(ngModel)]="patientDetails.Ethnicity" name="Ethnicity">
                                    @for (data of visitData?.RC; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Place of Birth</mat-label>
                                <mat-select [(ngModel)]="patientDetails.PlaceOfBirth" name="PlaceOfBirth">
                                    @for (data of visitData?.CY; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Nationality</mat-label>
                                <mat-select [(ngModel)]="patientDetails.Nationality" name="Nationality">
                                    @for (data of visitData?.NA; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox" name="idno">
                                <mat-label>Email</mat-label>
                                <input matInput type="email" [(ngModel)]="patientDetails.Email" name="email"
                                    #email="ngModel" required email aria-describedby="emailHelp" maxlength="50"
                                    autocomplete="off" />
                                @if (email.invalid && email.touched){
                                <mat-error class="text-xs">Please enter a valid email address
                                </mat-error>
                                }
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <br />
                <div class="flex w-100">
                    <div class="col-12">
                        <div class="range">
                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Religion</mat-label>
                                <mat-select [(ngModel)]="patientDetails.Religion" name="Religion">
                                    @for (data of visitData?.RE; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Language Spoken</mat-label>
                                <mat-select [(ngModel)]="patientDetails.LanguageCode" name="LanguageCode">
                                    @for (data of visitData?.LG; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Education</mat-label>
                                <mat-select [(ngModel)]="patientDetails.Education" name="Education">
                                    @for (data of visitData?.ED; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Occupation</mat-label>
                                <mat-select [(ngModel)]="patientDetails.Occupation" name="Occupation">
                                    @for (data of visitData?.ON; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" class="inputbox">
                                <mat-label>Marital Status</mat-label>
                                <mat-select [(ngModel)]="patientDetails.MaritalStatus" name="MaritalStatus">
                                    @for (data of visitData?.MS; track $index;){
                                    <mat-option [value]="data.IDENTIFIER">
                                        {{ data.DESCRIPTION }}
                                    </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div>
                    <form [formGroup]="myForm" class="flex gap-x-2 h-max">
                        <ngx-material-intl-tel-input [fieldControl]="myForm.get('phoneNumber')" [autoIpLookup]="false"
                            [appearance]="'outline'" [mainLabel]="'Address Phone 1'">
                        </ngx-material-intl-tel-input> <ngx-material-intl-tel-input
                            [fieldControl]="myForm.get('phoneNumber2')" [autoIpLookup]="false" [appearance]="'outline'"
                            [mainLabel]="'Address Phone 1'">
                        </ngx-material-intl-tel-input>
                    </form>
                </div>
                <br />
                <div class="flex space-x-2 w-full">
                    <section class="example-section">
                        <mat-checkbox class="example-margin" [checked]="
                            this.patientDetails.OrganDonor === 'Y'
                        " (change)="onCheckboxOrganDonor($event)">Organ Donor</mat-checkbox>

                        <mat-checkbox class="example-margin" [checked]="
                            this.patientDetails.SpeakEnglish === 'Y'
                        " (change)="onCheckboxSpeakEnglish($event)" name="SpeakEnglish">SpeakEnglish
                        </mat-checkbox>
                    </section>

                    <label for="label" class="text-lg font-bold text-center pt-1.5">AlertType :</label>
                    <section class="example-section">
                        <mat-checkbox class="example-margin" [checked]="
                                    this.patientDetails.AlertEmail === 'Y'
                                " (change)="onCheckboxAlertEmail($event)" name="AlertEmail">
                            AlertEmail</mat-checkbox>
                        <mat-checkbox class="example-margin" [checked]="this.patientDetails.AlertSMS === 'Y'"
                            (change)="onCheckboxAlertSMS($event)" name="AlertSMS">
                            AlertSMS</mat-checkbox>
                    </section>
                </div>

            </mat-tab>
            <mat-tab>
                <ng-template mat-tab-label>
                    <mat-icon svgIcon="assignment" class="text-blue-500"></mat-icon>
                    <strong class="text-blue-500">Address</strong>
                </ng-template>

                <div class="pt-2 w-100">
                    <!-- Use his-common-address component from app-appt -->
                    <his-common-address [addressData]="getAddressData()" [visitData]="visitData" [phoneForm]="myForm"
                        (addressChange)="onAddressChange($event)">
                    </his-common-address>
                </div>
            </mat-tab>
        </mat-tab-group>
    </mat-card>
</form>