import { MatSnackBar } from '@angular/material/snack-bar';
import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatDialog,  } from '@angular/material/dialog';
import {  Router, } from '@angular/router';
import { EcmedWebapiModelsPatientmgmtSearchPatientType, PatientService } from 'ecmed-api/visitmgmt';
import {
    ApptViewPopupComponent, PatientFormComponent,
    PatientIndividualFormComponent, CommonService,
} from 'his-components';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { ApplicationService } from 'ec-ngcore';
@Component({
    standalone: true,
    imports: [PatientFormComponent, MatIconModule, MatButtonModule, FillHeightDirective],
    templateUrl: './addpatient.component.html',
    styleUrl: './addpatient.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class AddpatientComponent implements OnInit {
    @ViewChild(PatientFormComponent) patientFormComponent?: PatientFormComponent;
    isLoading: boolean = false;
    patients: any[] = [];
    visitData: any;
    searchResults: any;
    savedindividualresult: any
    inputData: any;
    patientdata: any;
    patientresult: any;

    constructor(
        public _dialog: MatDialog,
        public snackbar: MatSnackBar,
        public commonServices: CommonService,
        public _API: PatientService,
        private _appService: ApplicationService,
        public router: Router) { }

    ngOnInit(): void {
        const codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
        this.commonServices.fetchVisitData(codeTypes).subscribe(
            (response: any) => {
                this.visitData = response;
            }
        );
    }

    savePatient() {
        this.patientFormComponent?.handleSaveUpdatePatient('save');
    }

    handleSave(event: any) {
        const patientData = JSON.parse(event);
        const requestParams = {
            ecmedWebapiModelsPatientmgmtEditModel: patientData
        };

        this._API.patientCreateNewPost(requestParams).subscribe((result: any) => {
            if (result) {
                // this.router.navigate(['view' + result], { relativeTo: this._currentRoute });
                this.router.navigate(['patients/view/' + result]);
            }
        });
    }

    goback() {
        this.router.navigate(['patients']);
    }

    onDateChanged(dateOfBirth: any): void {
        const dateOfBirthString: string = dateOfBirth.toISOString();
        this.commonServices.calculateAge(dateOfBirthString);
    }

    handleSearchData(searchData: any) {
        if (!searchData || !searchData.data) {
            return;
        }
       
        const { searchType, name, idNo, idType } = searchData.data;
        this._API.patientSearchIndividualGet( {
            searchType: searchType as EcmedWebapiModelsPatientmgmtSearchPatientType,
            name: name,
            iDNo: idNo,
            iDType: idType
        }).subscribe(
            (results) => {
                this.searchResults = results;
                if (this.searchResults.length === 0) {
                    this.openConfirmDialog();
                } else {
                    this.searchResults = results;
                }
            }
        );
    }

    handleEventData(eventData: any): void {
        if (eventData.action === 'search') {
            this.handleSearchData(eventData);
        }
    }

    openConfirmDialog(): void {
        this._appService.confirmDialog({
            message: 'No results found for your search. Do you want to create a new detail?',
        }).afterClosed().subscribe((result: any) => {
            if (result == 'Yes') {
                this.openDialog();
            }
        });

    }

    openDialog() {
        const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
            width: "2500px",
            height: "850px",
            panelClass: "custom-dialog-container",
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                this.fetchPatientDetails(result);
            }
        });
    }

    public fetchdetails(identifier: string) {
        this._API.patientGetIndividualDetailGet({ id: identifier }).subscribe(
            (data: object | object[]) => {
                this.inputData = data
                this.openDialog();
            }
        );
    }

    fetchPatientDetails(id: string): void {
        this._API.patientGetIndividualDetailGet({ id: id }).subscribe(
            (data: object | object[]) => {
                const patientDetails = Array.isArray(data) ? (data[0] as object) : (data as object);
                if (patientDetails) {
                    this.savedindividualresult = patientDetails;
                }
            }
        );
    }

}

